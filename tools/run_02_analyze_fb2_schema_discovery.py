#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# import os removed
import re

# Добавляем корневую директорию проекта в sys.path
import sys

# zipfile no longer required after refactor
from collections import defaultdict
from pathlib import Path as _P

sys.path.insert(0, str(_P(__file__).parent.parent))

# Импорт утилит
# Используем defusedxml для безопасного парсинга XML
import defusedxml.ElementTree as ET_safe

from tools.utils import collect_zip_archives, process_zip_archive  # noqa: E402

# =============================================================================
# НАСТРОЙКИ - УКАЖИТЕ ПУТИ К ПАПКАМ ДЛЯ СКАНИРОВАНИЯ
# =============================================================================
SCAN_PATHS = [
    r"/mnt/d/Project/books/zip/download/811194-815075",
    r"/mnt/d/Project/books/zip/download/815076-818518",
    r"/mnt/d/Project/books/zip/download/818519-822260",
    r"/mnt/d/Project/books/zip/download/822261-826220",
    r"/mnt/d/Project/books/zip/download/826221-829529",
]

# Имя выходного файла
OUTPUT_FILE = "result_fb2_structure_analyzer__opus.md"


class FB2StructureAnalyzer:
    def __init__(self):
        # Словарь для хранения уникальной структуры
        self.structure: defaultdict[str, defaultdict[str, set[str]]] = defaultdict(
            lambda: defaultdict(set)
        )
        self.processed_files = 0
        self.processed_archives = 0

    def scan_directories(self, paths):
        """Сканирует директории, собирает ZIP архивы через util и обрабатывает."""
        print("Начинаем сканирование директорий...")

        archives = collect_zip_archives(paths)
        if not archives:
            print("ZIP архивы не найдены")
            return

        for zip_path in archives:
            print(f"Обрабатываем архив: {zip_path}")
            self._process_zip_file(zip_path)

    def _scan_directory_recursive(self, directory):
        """(Deprecated) Оставлено для совместимости, больше не используется."""
        self.scan_directories([directory])

    def _process_zip_file(self, zip_path):
        """Обрабатывает ZIP используя util-функцию без дублирования."""

        def fb2_processor(archive_path: str, fb2_filename: str, temp_fb2_path: _P):
            print(f"  Обрабатываем FB2: {fb2_filename}")
            # Используем существующую логику
            try:
                with open(temp_fb2_path, "rb") as fb2_file:
                    content = fb2_file.read()

                # Парсим XML как раньше
                self._process_raw_fb2_bytes(content)

                self.processed_files += 1
            except Exception as exc:
                print(f"    Ошибка при обработке {fb2_filename}: {exc}")

        def error_handler(archive_path: str, _fb2_filename: str, exc: Exception):
            print(f"Ошибка архива {archive_path}: {exc}")

        # Учёт
        self.processed_archives += 1

        process_zip_archive(zip_path, fb2_processor, error_handler=error_handler)

    # Вынесенная логика обработки байтов FB2
    def _process_raw_fb2_bytes(self, content: bytes):
        # Пытаемся распарсить безопасно
        try:
            root = ET_safe.fromstring(content)
        except ET_safe.ParseError:
            content_str = content.decode("utf-8", errors="ignore")
            content_str = re.sub(r"<\?xml[^>]*\?>", "", content_str)
            root = ET_safe.fromstring(content_str)

        description = self._find_description(root)
        if description is not None:
            self._analyze_element_structure(description, "description")
        # else: игнорируем

    def _find_description(self, root):
        """Находит элемент description в FB2 файле"""
        # FB2 использует namespace, ищем description в любом namespace
        for elem in root.iter():
            if elem.tag.endswith("description"):
                return elem
        return None

    def _analyze_element_structure(self, element, path=""):
        """Рекурсивно анализирует структуру XML элемента"""
        current_path = path

        # Получаем имя тега без namespace
        tag_name = element.tag.split("}")[-1] if "}" in element.tag else element.tag

        if path:
            current_path = f"{path}.{tag_name}"
        else:
            current_path = tag_name

        # Сохраняем информацию о текстовом содержимом
        if element.text and element.text.strip():
            self.structure[current_path]["text_content"].add("(text content)")

        # Сохраняем информацию об атрибутах
        for attr_name, _attr_value in element.attrib.items():
            # Убираем namespace из имени атрибута
            clean_attr_name = (
                attr_name.split("}")[-1] if "}" in attr_name else attr_name
            )
            self.structure[current_path]["attributes"].add(f"@{clean_attr_name}")

        # Рекурсивно обрабатываем дочерние элементы
        for child in element:
            self._analyze_element_structure(child, current_path)

    def generate_markdown_report(self):
        """Генерирует отчет в формате Markdown"""
        print("\nГенерируем отчет...")
        print(f"Обработано архивов: {self.processed_archives}")
        print(f"Обработано FB2 файлов: {self.processed_files}")

        # Сортируем пути для удобного чтения
        sorted_paths = sorted(self.structure.keys())

        with open(OUTPUT_FILE, "w", encoding="utf-8") as f:
            f.write("# Структура метаданных FB2 книг\n\n")
            f.write(f"**Обработано архивов:** {self.processed_archives}\n")
            f.write(f"**Обработано FB2 файлов:** {self.processed_files}\n\n")
            f.write("## Найденная структура элементов\n\n")

            # Группируем по уровням вложенности для лучшего отображения
            self._write_structure_recursive(f, sorted_paths, "", 0)

    def _write_structure_recursive(self, file, all_paths, current_prefix, indent_level):
        """Рекурсивно записывает структуру в файл"""
        # Находим все прямые дочерние элементы текущего уровня
        children = set()
        prefix_len = len(current_prefix)

        for path in all_paths:
            if path.startswith(current_prefix):
                remainder = path[prefix_len:]
                if remainder.startswith("."):
                    remainder = remainder[1:]

                if remainder and "." in remainder:
                    # Это не прямой дочерний элемент
                    next_part = remainder.split(".")[0]
                    children.add(next_part)
                elif remainder:
                    # Это прямой дочерний элемент
                    children.add(remainder)

        # Записываем найденные элементы
        for child in sorted(children):
            full_path = f"{current_prefix}.{child}" if current_prefix else child
            indent = "  " * indent_level

            file.write(f"{indent}- {child}")

            # Добавляем информацию о содержимом и атрибутах
            if full_path in self.structure:
                extras: list[str] = []

                if self.structure[full_path]["text_content"]:
                    extras.extend(self.structure[full_path]["text_content"])

                if self.structure[full_path]["attributes"]:
                    extras.extend(sorted(self.structure[full_path]["attributes"]))

                if extras:
                    file.write(f" {' '.join(extras)}")

            file.write("\n")

            # Рекурсивно обрабатываем дочерние элементы
            self._write_structure_recursive(
                file, all_paths, full_path, indent_level + 1
            )


def main():
    """Главная функция программы"""
    print("FB2 Structure Analyzer")
    print("=" * 50)

    # Проверяем, что указаны пути для сканирования
    if not SCAN_PATHS:
        print("Ошибка: Не указаны пути для сканирования в переменной SCAN_PATHS")
        return

    # Создаем анализатор и запускаем обработку
    analyzer = FB2StructureAnalyzer()

    try:
        # Сканируем директории
        analyzer.scan_directories(SCAN_PATHS)

        # Генерируем отчет
        if analyzer.processed_files > 0:
            analyzer.generate_markdown_report()
            print(f"\nОтчет сохранен в файл: {OUTPUT_FILE}")
        else:
            print("\nНе найдено ни одного FB2 файла для обработки")

    except KeyboardInterrupt:
        print("\nОбработка прервана пользователем")
    except Exception as e:
        print(f"Критическая ошибка: {e}")


if __name__ == "__main__":
    main()
