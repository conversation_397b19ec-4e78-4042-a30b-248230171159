# run_01_analyze_fb2_metadata.py

import logging
import re
import sys
from pathlib import Path

# Добавляем корневую директорию проекта в sys.path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Импортируем утилиты проекта
from tools.utils import collect_zip_archives, process_zip_archive  # noqa: E402

# --- НАСТРОЙКИ СКРИПТА ---

# 1. Укажите путь к директории с архивами книг.
#    Скрипт будет рекурсивно искать все .zip файлы внутри этой папки.
SOURCE_DIRECTORY = Path(
    "/mnt/d/Project/books/zip"
)  # /mnt/d/Project/books/zip/zip_flibusta

# 2. Укажите, сколько файлов обработать.
#    - Чтобы обработать ВСЕ найденные файлы, установите значение None
#    - Чтобы обработать только первые N файлов, укажите число (например, 1000)
FILES_LIMIT = None  # 1000  # Установите None для обработки всех файлов

# 3. Имя выходного файла для сохранения результатов анализа.
OUTPUT_FILE = "fb2_metadata_analysis.md"

# --- КОНЕЦ НАСТРОЕК ---


def setup_logging():
    """Настраивает логирование для вывода информации в консоль."""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(message)s",
        stream=sys.stdout,
    )


def main():
    """Основная функция для сканирования и анализа метаданных FB2."""
    setup_logging()
    logger = logging.getLogger(__name__)

    # --- Шаг 1: Проверка и подготовка ---
    if not SOURCE_DIRECTORY.is_dir():
        logger.critical(f"ОШИБКА: Директория не найдена: {SOURCE_DIRECTORY}")
        sys.exit(1)

    logger.info(f"Начинаю сканирование директории: {SOURCE_DIRECTORY}")

    # Собираем архивы через утилиту
    all_zip_files = collect_zip_archives([str(SOURCE_DIRECTORY)])
    if not all_zip_files:
        logger.warning("В указанной директории не найдено ни одного .zip файла.")
        return

    total_found = len(all_zip_files)
    logger.info(f"Найдено {total_found} zip-архивов.")

    # Применяем лимит, если он установлен
    if FILES_LIMIT is not None and FILES_LIMIT > 0:
        files_to_process = all_zip_files[:FILES_LIMIT]
        logger.info(
            f"Будет обработано файлов (согласно лимиту): {len(files_to_process)}"
        )
    else:
        files_to_process = all_zip_files
        logger.info("Будут обработаны ВСЕ найденные файлы.")

    # Регулярное выражение для извлечения блока <description>
    # re.DOTALL позволяет точке '.' соответствовать символу новой строки
    # re.IGNORECASE делает поиск нечувствительным к регистру тегов (<description> или <DESCRIPTION>)
    description_pattern = re.compile(
        r"<description>.*?</description>", re.DOTALL | re.IGNORECASE
    )

    processed_count = 0

    # --- Шаг 2: Обработка файлов и извлечение метаданных ---
    try:
        with open(OUTPUT_FILE, "w", encoding="utf-8") as out_f:
            logger.info(f"Результаты будут сохранены в файл: {OUTPUT_FILE}")

            total_to_process = len(files_to_process)

            for i, zip_path in enumerate(files_to_process, 1):
                logger.info(
                    f"[{i}/{total_to_process}] Обрабатываю архив: {Path(zip_path).name}"
                )

                processed_first_fb2 = False

                def fb2_processor(
                    archive_path: str, fb2_filename: str, temp_fb2_path: Path
                ):
                    nonlocal processed_first_fb2, processed_count
                    if processed_first_fb2:
                        return  # Обрабатываем только первый FB2 в архиве

                    try:
                        content_bytes = temp_fb2_path.read_bytes()

                        # Пробуем разные кодировки
                        content_str = None
                        for enc in ("utf-8", "windows-1251", "cp1251"):
                            try:
                                content_str = content_bytes.decode(enc)
                                break
                            except UnicodeDecodeError:
                                continue

                        if content_str is None:
                            logger.error(
                                f"  -> Не удалось декодировать файл {fb2_filename}. Пропускаю."
                            )
                            return

                        match = description_pattern.search(content_str)
                        if match:
                            out_f.write(f"// --- File: {fb2_filename} ---\n")
                            out_f.write(match.group(0))
                            out_f.write("\n\n")
                            processed_count += 1
                        else:
                            logger.warning(
                                f"  -> В файле {fb2_filename} не найден блок <description>. Пропускаю."
                            )

                    finally:
                        processed_first_fb2 = True  # Больше Fb2 из архива не нужно

                def error_handler(
                    archive_path: str, _fb2_filename: str, exc: Exception
                ):
                    logger.error(
                        f"  -> Ошибка при обработке архива {Path(archive_path).name}: {exc}"
                    )

                process_zip_archive(
                    zip_path=zip_path,
                    fb2_processor=fb2_processor,
                    error_handler=error_handler,
                )

    except IOError as e:
        logger.critical(
            f"Не удалось записать в выходной файл {OUTPUT_FILE}. Ошибка: {e}"
        )
        sys.exit(1)

    # --- Шаг 3: Завершение ---
    logger.info("=" * 50)
    logger.info("Анализ метаданных FB2 завершен.")
    logger.info(f"Всего обработано архивов: {len(files_to_process)}")
    logger.info(f"Извлечено блоков метаданных: {processed_count}")
    logger.info(f"Результат сохранен в файле: {OUTPUT_FILE}")
    logger.info("=" * 50)


if __name__ == "__main__":
    main()
